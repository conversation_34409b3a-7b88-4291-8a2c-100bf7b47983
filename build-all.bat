@echo off
echo ===== TCP转发工具跨平台打包脚本 =====
echo.

echo 检查 pkg 是否已安装...
pkg --version >nul 2>&1
if %errorlevel% neq 0 (
    echo pkg 未安装，正在安装...
    npm install -g pkg
    if %errorlevel% neq 0 (
        echo 安装 pkg 失败，请手动安装: npm install -g pkg
        pause
        exit /b 1
    )
)

echo pkg 已安装，开始跨平台打包...
echo.

echo 创建输出目录...
if not exist dist mkdir dist
if not exist dist\windows mkdir dist\windows
if not exist dist\linux mkdir dist\linux
if not exist dist\macos mkdir dist\macos

echo 打包 Windows x64 版本...
pkg tcp-server.js --targets node18-win-x64 --output dist\windows\tcp-server.exe
if %errorlevel% neq 0 (
    echo Windows 版本打包失败
    pause
    exit /b 1
)

echo 打包 Linux x64 版本...
pkg tcp-server.js --targets node18-linux-x64 --output dist\linux\tcp-server
if %errorlevel% neq 0 (
    echo Linux 版本打包失败
    pause
    exit /b 1
)

echo 打包 macOS x64 版本...
pkg tcp-server.js --targets node18-macos-x64 --output dist\macos\tcp-server
if %errorlevel% neq 0 (
    echo macOS 版本打包失败
    pause
    exit /b 1
)

echo.
echo 复制配置文件和文档...
copy device-mapping.json dist\windows\
copy device-mapping.json dist\linux\
copy device-mapping.json dist\macos\
copy *.md dist\windows\
copy *.md dist\linux\
copy *.md dist\macos\

echo.
echo ===== 跨平台打包完成 =====
echo.
echo Windows 版本: dist\windows\tcp-server.exe
echo Linux 版本:   dist\linux\tcp-server
echo macOS 版本:   dist\macos\tcp-server
echo.
echo 每个平台目录都包含:
echo - 可执行文件
echo - device-mapping.json (配置文件)
echo - 说明文档 (*.md)
echo.
pause
