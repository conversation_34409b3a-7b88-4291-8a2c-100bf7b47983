# 两设备互发模式说明

## 概述

TCP服务器现在配置为两设备互发模式，只允许两台指定的设备之间进行数据转发，其他设备的数据将被丢弃。

## 配置说明

### device-mapping.json 配置

```json
{
  "deviceMappings": {
    "0x12345678": ["0x87654321"],
    "0x87654321": ["0x12345678"]
  },
  "defaultBehavior": "drop"
}
```

### 配置参数说明

- **设备A ID**: `0x12345678` - 数据转发给设备B
- **设备B ID**: `0x87654321` - 数据转发给设备A
- **默认行为**: `drop` - 丢弃未配置设备的数据

## 工作原理

### 数据流程

1. **设备A发送数据**:
   ```
   发送: [12345678][DATA] → TCP服务器
   转发: [87654321][DATA] → 设备B
   ```

2. **设备B发送数据**:
   ```
   发送: [87654321][DATA] → TCP服务器
   转发: [12345678][DATA] → 设备A
   ```

### ID替换机制

- 服务器接收到数据包后，解析前4字节的源设备ID
- 根据映射配置找到目标设备ID
- 将数据包前4字节替换为目标设备ID
- 转发给目标设备

## 使用方法

### 1. 启动服务器
```bash
npm start
```

服务器启动后会显示：
```
设备映射配置加载成功:
  映射规则数量: 2
  默认行为: drop
  映射规则:
    12345678 -> [87654321]
    87654321 -> [12345678]
TCP服务器启动成功
监听端口: 6000
等待设备连接...
```

### 2. 运行两设备测试
```bash
npm test
```
或者
```bash
npm run test-two
```

### 3. 预期测试结果

**连接阶段:**
```
[设备A] 已连接到服务器 (ID: 12345678)
[设备B] 已连接到服务器 (ID: 87654321)
```

**数据转发测试:**
```
[设备A] 发送数据 (A->B):
  源设备ID: 12345678
  发送数据包: 1234567811223344
  实际数据: 11223344

[设备B] 收到转发数据:
  完整数据包: 8765432111223344
  目标设备ID: 87654321 (应该是我的ID: 87654321)
  实际数据: 11223344
  ID替换正确!
```

## 服务器端日志示例

### 设备连接
```
[1] 设备注册成功: ID=12345678
  地址: 127.0.0.1:54321
  连接时间: 2024-08-02 10:30:15
  转发规则: 12345678 -> [87654321]

[2] 设备注册成功: ID=87654321
  地址: 127.0.0.1:54322
  连接时间: 2024-08-02 10:30:16
  转发规则: 87654321 -> [12345678]
```

### 数据转发
```
[1] 收到数据: 8 字节
[1] 解析数据包:
  源设备ID: 12345678
  数据长度: 4 字节
  数据内容: 11223344

使用映射规则转发: 12345678 -> [87654321]
数据转发成功: 12345678 -> 87654321
  原始数据: 11223344
  转发数据包: 8765432111223344
  ID替换: 12345678 -> 87654321
数据转发完成，成功转发给 1 个设备
```

## 安全特性

### 1. 设备隔离
- 只有配置的两台设备可以通信
- 其他设备连接后发送的数据会被丢弃
- 防止未授权设备接入网络

### 2. ID验证
- 严格验证设备ID
- 只转发给映射配置中指定的目标设备
- 自动替换ID确保数据包格式正确

## 配置修改

### 更换设备ID
修改 `device-mapping.json` 文件：

```json
{
  "deviceMappings": {
    "0xAAAABBBB": ["0xCCCCDDDD"],
    "0xCCCCDDDD": ["0xAAAABBBB"]
  },
  "defaultBehavior": "drop"
}
```

重启服务器应用新配置。

### 端口配置
通过环境变量修改端口：
```bash
PORT=8080 npm start
```

## 应用场景

1. **点对点通信**: 两台设备之间的专用通信通道
2. **数据中继**: 服务器作为两台设备之间的数据中继站
3. **协议转换**: 在两台不同协议的设备之间进行数据转换
4. **安全隧道**: 提供安全的设备间通信通道
5. **测试环境**: 模拟两台设备之间的通信进行测试

## 故障排除

### 1. 设备连接失败
- 检查服务器是否启动
- 确认端口号正确（默认6000）
- 检查防火墙设置

### 2. 数据不转发
- 确认设备ID在配置文件中
- 检查数据包格式（前4字节必须是设备ID）
- 查看服务器日志确认数据是否被接收

### 3. ID替换错误
- 检查映射配置是否正确
- 确认目标设备在线
- 验证数据包长度（至少8字节）

## 性能特点

- **低延迟**: 直接转发，无额外处理
- **高可靠**: 基于TCP协议，保证数据传输
- **简单配置**: JSON配置文件，易于修改
- **实时监控**: 详细的日志输出，便于调试
