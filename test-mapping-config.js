const net = require('net');

// 测试设备映射配置的客户端
class MappingTestDevice {
    constructor(deviceId, name, serverHost = 'localhost', serverPort = 8080) {
        this.deviceId = deviceId;
        this.deviceIdHex = deviceId.toString(16).toUpperCase().padStart(8, '0');
        this.name = name;
        this.serverHost = serverHost;
        this.serverPort = serverPort;
        this.socket = null;
        this.connected = false;
        this.receivedMessages = [];
    }

    // 创建数据包
    createDataPacket(data) {
        const buffer = Buffer.alloc(4 + data.length);
        buffer.writeUInt32BE(this.deviceId, 0);
        data.copy(buffer, 4);
        return buffer;
    }

    // 连接到服务器
    connect() {
        return new Promise((resolve, reject) => {
            this.socket = new net.Socket();
            
            this.socket.connect(this.serverPort, this.serverHost, () => {
                this.connected = true;
                console.log(`[${this.name}] 设备ID ${this.deviceIdHex} 已连接到服务器`);
                resolve();
            });

            this.socket.on('data', (buffer) => {
                console.log(`\n[${this.name}] 收到数据:`);
                console.log(`  完整数据包: ${buffer.toString('hex').toUpperCase()}`);
                
                // 解析收到的数据包
                if (buffer.length >= 4) {
                    const receivedDeviceId = buffer.readUInt32BE(0);
                    const receivedDeviceIdHex = receivedDeviceId.toString(16).toUpperCase().padStart(8, '0');
                    const data = buffer.slice(4);
                    
                    console.log(`  目标设备ID: ${receivedDeviceIdHex} (我的ID: ${this.deviceIdHex})`);
                    console.log(`  实际数据: ${data.toString('hex').toUpperCase()}`);
                    console.log(`  ID映射${receivedDeviceId === this.deviceId ? '正确' : '错误'}!`);
                    
                    // 记录收到的消息
                    this.receivedMessages.push({
                        timestamp: new Date().toISOString(),
                        targetDeviceId: receivedDeviceId,
                        data: data,
                        isCorrectMapping: receivedDeviceId === this.deviceId
                    });
                }
                console.log('---');
            });

            this.socket.on('error', (err) => {
                console.error(`[${this.name}] 连接错误:`, err.message);
                this.connected = false;
                reject(err);
            });

            this.socket.on('close', () => {
                console.log(`[${this.name}] 连接已关闭`);
                this.connected = false;
            });
        });
    }

    // 发送数据
    sendData(data, description = '') {
        if (!this.connected || !this.socket) {
            console.error(`[${this.name}] 未连接到服务器`);
            return false;
        }

        try {
            const packet = this.createDataPacket(data);
            this.socket.write(packet);
            
            console.log(`\n[${this.name}] 发送数据 ${description}:`);
            console.log(`  源设备ID: ${this.deviceIdHex}`);
            console.log(`  发送数据包: ${packet.toString('hex').toUpperCase()}`);
            console.log(`  实际数据: ${data.toString('hex').toUpperCase()}`);
            console.log('---');
            return true;
        } catch (error) {
            console.error(`[${this.name}] 发送数据失败:`, error);
            return false;
        }
    }

    // 获取接收统计
    getReceiveStats() {
        return {
            totalReceived: this.receivedMessages.length,
            correctMappings: this.receivedMessages.filter(msg => msg.isCorrectMapping).length,
            incorrectMappings: this.receivedMessages.filter(msg => !msg.isCorrectMapping).length
        };
    }

    // 断开连接
    disconnect() {
        if (this.socket) {
            this.socket.destroy();
            this.socket = null;
            this.connected = false;
        }
    }
}

// 测试映射配置功能
async function testMappingConfig() {
    console.log('=== 设备映射配置测试 ===\n');
    console.log('根据 device-mapping.json 配置:');
    console.log('  设备A (12345678) -> [设备B (87654321), 设备M (AABBCCDD)]');
    console.log('  设备B (87654321) -> [设备A (12345678)]');
    console.log('  设备M (AABBCCDD) -> [设备N (DDCCBBAA)]');
    console.log('  设备N (DDCCBBAA) -> [设备A (12345678), 设备B (87654321), 设备M (AABBCCDD)]');
    console.log('');

    // 创建测试设备
    const deviceA = new MappingTestDevice(0x12345678, '设备A'); // 应该转发给B和M
    const deviceB = new MappingTestDevice(0x87654321, '设备B'); // 应该转发给A
    const deviceM = new MappingTestDevice(0xAABBCCDD, '设备M'); // 应该转发给N
    const deviceN = new MappingTestDevice(0xDDCCBBAA, '设备N'); // 应该转发给A、B、M

    try {
        // 连接所有设备
        console.log('1. 连接设备到服务器...\n');
        await deviceA.connect();
        await new Promise(resolve => setTimeout(resolve, 500));
        
        await deviceB.connect();
        await new Promise(resolve => setTimeout(resolve, 500));
        
        await deviceM.connect();
        await new Promise(resolve => setTimeout(resolve, 500));
        
        await deviceN.connect();
        await new Promise(resolve => setTimeout(resolve, 1000));

        console.log('\n2. 开始映射配置测试...\n');

        // 测试1: 设备A发送数据 -> 应该只转发给B和M
        setTimeout(() => {
            const testData1 = Buffer.from([0x11, 0x22, 0x33, 0x44]);
            deviceA.sendData(testData1, '(应该转发给B和M)');
        }, 2000);

        // 测试2: 设备B发送数据 -> 应该只转发给A
        setTimeout(() => {
            const testData2 = Buffer.from([0x55, 0x66, 0x77, 0x88]);
            deviceB.sendData(testData2, '(应该转发给A)');
        }, 4000);

        // 测试3: 设备M发送数据 -> 应该只转发给N
        setTimeout(() => {
            const testData3 = Buffer.from([0xAA, 0xBB, 0xCC]);
            deviceM.sendData(testData3, '(应该转发给N)');
        }, 6000);

        // 测试4: 设备N发送数据 -> 应该转发给A、B、M
        setTimeout(() => {
            const testData4 = Buffer.from([0xFF, 0xEE, 0xDD]);
            deviceN.sendData(testData4, '(应该转发给A、B、M)');
        }, 8000);

        // 15秒后显示统计并断开连接
        setTimeout(() => {
            console.log('\n3. 测试结果统计...\n');
            
            const devices = [deviceA, deviceB, deviceM, deviceN];
            devices.forEach(device => {
                const stats = device.getReceiveStats();
                console.log(`[${device.name}] 接收统计:`);
                console.log(`  总接收: ${stats.totalReceived} 条`);
                console.log(`  正确映射: ${stats.correctMappings} 条`);
                console.log(`  错误映射: ${stats.incorrectMappings} 条`);
                console.log('');
            });
            
            console.log('4. 断开所有连接...\n');
            devices.forEach(device => device.disconnect());
            
            console.log('=== 映射配置测试结束 ===');
            console.log('检查上述结果是否符合映射配置的预期');
            process.exit(0);
        }, 15000);

    } catch (error) {
        console.error('测试失败:', error);
        process.exit(1);
    }
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
    testMappingConfig();
}

module.exports = MappingTestDevice;
