const net = require("net");
const fs = require("fs");
const path = require("path");

class TCPServer {
  constructor(config) {
    this.port = config.port || 8080;
    this.server = null;
    this.deviceConnections = new Map(); // 存储设备ID到连接的映射
    this.connectionToDevice = new Map(); // 存储连接到设备ID的映射
    this.deviceInfo = new Map(); // 存储设备详细信息：deviceId -> {socket, address, port, connId, connectTime}
    this.allSockets = new Set(); // 存储所有socket连接
    this.connectionId = 0;

    // 设备映射配置
    this.deviceMappings = new Map(); // sourceDeviceId -> [targetDeviceId1, targetDeviceId2, ...]
    this.defaultBehavior = "broadcast"; // 'broadcast' 或 'drop'
    this.mappingConfigFile = config.mappingConfigFile || "device-mapping.json";

    // 加载映射配置
    this.loadMappingConfig();
  }

  // 解析设备ID - 支持多种格式
  parseDeviceId(idStr) {
    if (typeof idStr === "number") {
      return idStr;
    }

    const str = idStr.toString().trim();

    // 支持十六进制格式: 0x12345678, 0X12345678, 12345678
    if (str.startsWith("0x") || str.startsWith("0X")) {
      return parseInt(str, 16);
    }

    // 支持纯数字格式
    if (/^\d+$/.test(str)) {
      return parseInt(str, 10);
    }

    // 支持纯十六进制格式 (不带0x前缀)
    if (/^[0-9A-Fa-f]+$/.test(str)) {
      return parseInt(str, 16);
    }

    throw new Error(`无效的设备ID格式: ${idStr}`);
  }

  // 加载设备映射配置
  loadMappingConfig() {
    try {
      if (fs.existsSync(this.mappingConfigFile)) {
        const configData = fs.readFileSync(this.mappingConfigFile, "utf8");
        const config = JSON.parse(configData);

        // 解析设备映射
        if (config.deviceMappings) {
          this.deviceMappings.clear();
          for (const [sourceId, targetIds] of Object.entries(
            config.deviceMappings
          )) {
            try {
              const sourceDeviceId = this.parseDeviceId(sourceId);
              const targetDeviceIds = Array.isArray(targetIds)
                ? targetIds.map((id) => this.parseDeviceId(id))
                : [this.parseDeviceId(targetIds)];
              this.deviceMappings.set(sourceDeviceId, targetDeviceIds);
            } catch (error) {
              console.error(
                `解析设备ID失败: ${sourceId} -> ${targetIds}`,
                error.message
              );
            }
          }
        }

        // 设置默认行为
        this.defaultBehavior = config.defaultBehavior || "broadcast";

        console.log("设备映射配置加载成功:");
        console.log(`  映射规则数量: ${this.deviceMappings.size}`);
        console.log(`  默认行为: ${this.defaultBehavior}`);
        this.printMappingConfig();
      } else {
        console.log(
          `映射配置文件 ${this.mappingConfigFile} 不存在，使用默认广播模式`
        );
      }
    } catch (error) {
      console.error("加载映射配置失败:", error.message);
      console.log("使用默认广播模式");
    }
  }

  // 打印映射配置
  printMappingConfig() {
    console.log("  映射规则:");
    for (const [sourceId, targetIds] of this.deviceMappings) {
      const sourceHex = sourceId.toString(16).toUpperCase().padStart(8, "0");
      const targetHexs = targetIds.map((id) =>
        id.toString(16).toUpperCase().padStart(8, "0")
      );
      console.log(`    ${sourceHex} -> [${targetHexs.join(", ")}]`);
    }
  }

  // 解析数据包，提取设备ID
  parseDataPacket(buffer) {
    if (buffer.length < 8) {
      return null;
    }

    try {
      // 提取4字节设备ID (前4个字节)
      const deviceId = buffer.readUInt32BE(0);
      const data = buffer.slice(4); // 剩余数据

      return {
        deviceId: deviceId,
        deviceIdHex: deviceId.toString(16).toUpperCase().padStart(8, "0"),
        data: data,
        totalLength: buffer.length,
      };
    } catch (error) {
      console.error("解析数据包错误:", error);
      return null;
    }
  }

  // 构建数据包
  buildDataPacket(deviceId, data) {
    const buffer = Buffer.alloc(4 + data.length);
    buffer.writeUInt32BE(deviceId, 0);
    data.copy(buffer, 4);
    return buffer;
  }

  // 注册设备连接
  registerDevice(socket, deviceId) {
    const connId = socket.connId;
    const deviceIdHex = deviceId.toString(16).toUpperCase().padStart(8, "0");

    // 如果设备ID已存在，断开旧连接
    if (this.deviceConnections.has(deviceId)) {
      const oldSocket = this.deviceConnections.get(deviceId);
      console.log(`[${connId}] 设备ID ${deviceIdHex} 已存在，断开旧连接`);
      oldSocket.destroy();
    }

    // 注册新连接
    this.deviceConnections.set(deviceId, socket);
    this.connectionToDevice.set(socket, deviceId);

    // 记录设备详细信息
    this.deviceInfo.set(deviceId, {
      socket: socket,
      address: socket.remoteAddress,
      port: socket.remotePort,
      connId: connId,
      connectTime: new Date().toISOString(),
    });

    console.log(`[${connId}] 设备注册成功: ID=${deviceIdHex}`);
    console.log(`  地址: ${socket.remoteAddress}:${socket.remotePort}`);
    console.log(`  连接时间: ${new Date().toLocaleString()}`);
    console.log(`[${connId}] 当前在线设备数: ${this.deviceConnections.size}`);

    // 显示该设备的转发规则
    this.showDeviceForwardingRules(deviceId);
  }

  // 显示设备的转发规则
  showDeviceForwardingRules(deviceId) {
    const deviceIdHex = deviceId.toString(16).toUpperCase().padStart(8, "0");

    if (this.deviceMappings.has(deviceId)) {
      const targetIds = this.deviceMappings.get(deviceId);
      const targetHexs = targetIds.map((id) =>
        id.toString(16).toUpperCase().padStart(8, "0")
      );
      console.log(`  转发规则: ${deviceIdHex} -> [${targetHexs.join(", ")}]`);
    } else {
      console.log(
        `  转发规则: ${deviceIdHex} -> ${
          this.defaultBehavior === "broadcast"
            ? "广播给所有其他设备"
            : "丢弃数据"
        }`
      );
    }
  }

  // 转发数据到目标设备
  forwardData(sourceDeviceId, targetDeviceId, data) {
    const targetSocket = this.deviceConnections.get(targetDeviceId);

    if (!targetSocket) {
      console.log(
        `目标设备 ${targetDeviceId
          .toString(16)
          .toUpperCase()} 不在线，无法转发数据`
      );
      return false;
    }

    try {
      // 构建转发数据包 - 关键：将目标设备ID替换到前4字节
      const packet = this.buildDataPacket(targetDeviceId, data);
      targetSocket.write(packet);

      console.log(
        `数据转发成功: ${sourceDeviceId
          .toString(16)
          .toUpperCase()} -> ${targetDeviceId.toString(16).toUpperCase()}`
      );
      console.log(`  原始数据: ${data.toString("hex").toUpperCase()}`);
      console.log(`  转发数据包: ${packet.toString("hex").toUpperCase()}`);
      console.log(
        `  ID替换: ${sourceDeviceId
          .toString(16)
          .toUpperCase()} -> ${targetDeviceId.toString(16).toUpperCase()}`
      );
      return true;
    } catch (error) {
      console.error("数据转发失败:", error);
      return false;
    }
  }

  // 移除设备连接
  unregisterDevice(socket) {
    const deviceId = this.connectionToDevice.get(socket);
    if (deviceId) {
      this.deviceConnections.delete(deviceId);
      this.connectionToDevice.delete(socket);
      console.log(`设备断开连接: ID=${deviceId.toString(16).toUpperCase()}`);
      console.log(`当前在线设备数: ${this.deviceConnections.size}`);
    }
  }

  // 获取在线设备列表
  getOnlineDevices() {
    return Array.from(this.deviceConnections.keys()).map((id) =>
      id.toString(16).toUpperCase().padStart(8, "0")
    );
  }

  start() {
    this.server = net.createServer((socket) => {
      const connId = ++this.connectionId;
      socket.connId = connId;

      // 将socket添加到集合中
      this.allSockets.add(socket);

      console.log(
        `[${connId}] 新连接: ${socket.remoteAddress}:${socket.remotePort}`
      );

      // 在两设备模式下，连接时先不注册设备ID，等收到数据时再处理
      console.log(`[${connId}] 等待设备发送数据以确定设备ID...`);

      // 处理接收到的数据
      socket.on("data", (buffer) => {
        console.log(`[${connId}] 收到数据: ${buffer.length} 字节`);

        // 解析数据包
        const packet = this.parseDataPacket(buffer);
        if (!packet) {
          console.log(`[${connId}] 数据包格式错误，忽略`);
          return;
        }

        console.log(`[${connId}] 解析数据包:`);
        console.log(`  源设备ID: ${packet.deviceIdHex}`);
        console.log(`  数据长度: ${packet.data.length} 字节`);
        console.log(`  数据内容: ${packet.data.toString("hex").toUpperCase()}`);

        // 如果这是设备第一次发送数据，注册设备
        if (!this.connectionToDevice.has(socket)) {
          this.registerDevice(socket, packet.deviceId);
        }

        // 根据映射配置转发数据（这里会自动处理目标设备的注册）
        this.forwardDataByMapping(packet.deviceId, packet.data);
      });

      // 连接关闭处理
      socket.on("close", () => {
        console.log(`[${connId}] 连接关闭`);
        this.allSockets.delete(socket);
        this.unregisterDevice(socket);
      });

      // 错误处理
      socket.on("error", (err) => {
        console.error(`[${connId}] 连接错误:`, err.message);
        this.allSockets.delete(socket);
        this.unregisterDevice(socket);
      });
    });

    this.server.listen(this.port, () => {
      console.log(`TCP服务器启动成功`);
      console.log(`监听端口: ${this.port}`);
      console.log(`等待设备连接...`);
      console.log("---");
    });

    this.server.on("error", (err) => {
      console.error("服务器错误:", err);
    });
  }

  // 根据映射配置转发数据
  forwardDataByMapping(sourceDeviceId, data) {
    const sourceDeviceIdHex = sourceDeviceId
      .toString(16)
      .toUpperCase()
      .padStart(8, "0");
    let forwardCount = 0;

    // 检查是否有特定的映射规则
    if (this.deviceMappings.has(sourceDeviceId)) {
      const targetDeviceIds = this.deviceMappings.get(sourceDeviceId);
      console.log(
        `使用映射规则转发: ${sourceDeviceIdHex} -> [${targetDeviceIds
          .map((id) => id.toString(16).toUpperCase().padStart(8, "0"))
          .join(", ")}]`
      );

      for (const targetDeviceId of targetDeviceIds) {
        // 关键改动：不检查目标设备是否已注册，直接查找连接
        const targetSocket = this.findSocketForTargetDevice(
          targetDeviceId,
          sourceDeviceId
        );

        if (targetSocket) {
          if (this.forwardData(sourceDeviceId, targetDeviceId, data)) {
            forwardCount++;
          }
        } else {
          const targetHex = targetDeviceId
            .toString(16)
            .toUpperCase()
            .padStart(8, "0");
          console.log(`目标设备 ${targetHex} 不在线，跳过转发`);
        }
      }
    } else {
      // 使用默认行为
      if (this.defaultBehavior === "broadcast") {
        console.log(`使用默认广播模式: ${sourceDeviceIdHex} -> 所有其他设备`);
        forwardCount = this.broadcastToOtherDevices(sourceDeviceId, data);
      } else {
        console.log(`使用默认丢弃模式: ${sourceDeviceIdHex} -> 数据被丢弃`);
      }
    }

    console.log(`数据转发完成，成功转发给 ${forwardCount} 个设备`);
    return forwardCount;
  }

  // 查找目标设备的连接
  findSocketForTargetDevice(targetDeviceId, sourceDeviceId) {
    // 首先检查目标设备是否已经注册
    if (this.deviceConnections.has(targetDeviceId)) {
      return this.deviceConnections.get(targetDeviceId);
    }

    // 查找所有socket连接中未注册的连接
    for (const socket of this.allSockets) {
      const registeredDeviceId = this.connectionToDevice.get(socket);

      // 如果这个socket没有注册设备ID，就假设它是目标设备
      if (!registeredDeviceId) {
        console.log(
          `发现未注册连接，假设为目标设备 ${targetDeviceId
            .toString(16)
            .toUpperCase()
            .padStart(8, "0")}`
        );
        // 注册这个设备
        this.registerDeviceWithId(socket, targetDeviceId);
        return socket;
      }

      // 如果注册的设备ID不是源设备，也可以考虑（但这种情况应该很少）
      if (registeredDeviceId !== sourceDeviceId) {
        // 这里可以添加额外的逻辑，比如检查是否是我们期望的目标设备
        continue;
      }
    }

    console.log(
      `未找到可用连接用于目标设备 ${targetDeviceId
        .toString(16)
        .toUpperCase()
        .padStart(8, "0")}`
    );
    return null;
  }

  // 使用指定ID注册设备
  registerDeviceWithId(socket, deviceId) {
    const connId = socket.connId;
    const deviceIdHex = deviceId.toString(16).toUpperCase().padStart(8, "0");

    // 如果设备ID已存在，断开旧连接
    if (this.deviceConnections.has(deviceId)) {
      const oldSocket = this.deviceConnections.get(deviceId);
      console.log(`[${connId}] 设备ID ${deviceIdHex} 已存在，断开旧连接`);
      oldSocket.destroy();
    }

    // 注册新连接
    this.deviceConnections.set(deviceId, socket);
    this.connectionToDevice.set(socket, deviceId);

    // 记录设备详细信息
    this.deviceInfo.set(deviceId, {
      socket: socket,
      address: socket.remoteAddress,
      port: socket.remotePort,
      connId: connId,
      connectTime: new Date().toISOString(),
    });

    console.log(`[${connId}] 设备自动注册: ID=${deviceIdHex}`);
    console.log(`  地址: ${socket.remoteAddress}:${socket.remotePort}`);
    console.log(`  连接时间: ${new Date().toLocaleString()}`);
    console.log(`[${connId}] 当前在线设备数: ${this.deviceConnections.size}`);

    // 显示该设备的转发规则
    this.showDeviceForwardingRules(deviceId);
  }

  // 广播数据给其他设备（默认行为）
  broadcastToOtherDevices(sourceDeviceId, data) {
    let forwardCount = 0;

    for (const [deviceId] of this.deviceConnections) {
      if (deviceId !== sourceDeviceId) {
        if (this.forwardData(sourceDeviceId, deviceId, data)) {
          forwardCount++;
        }
      }
    }

    return forwardCount;
  }

  // 转发数据给指定设备
  forwardToSpecificDevice(sourceDeviceId, targetDeviceId, data) {
    return this.forwardData(sourceDeviceId, targetDeviceId, data);
  }

  stop() {
    if (this.server) {
      this.server.close();
      console.log("TCP服务器已停止");
    }

    // 关闭所有连接
    for (const socket of this.deviceConnections.values()) {
      socket.destroy();
    }

    this.deviceConnections.clear();
    this.connectionToDevice.clear();
  }
}

// 解析命令行参数
function parseCommandLineArgs() {
  const args = process.argv.slice(2);
  let port = process.env.PORT || 8080;
  let configFile = "device-mapping.json";

  for (let i = 0; i < args.length; i++) {
    if (args[i] === "--port" || args[i] === "-p") {
      port = parseInt(args[i + 1]) || port;
      i++; // 跳过下一个参数
    } else if (args[i] === "--config" || args[i] === "-c") {
      configFile = args[i + 1] || configFile;
      i++; // 跳过下一个参数
    } else if (args[i] === "--help" || args[i] === "-h") {
      console.log("TCP设备转发服务器");
      console.log("");
      console.log("用法:");
      console.log("  node tcp-server.js [选项]");
      console.log("");
      console.log("选项:");
      console.log("  -p, --port <端口>     指定监听端口 (默认: 8080)");
      console.log(
        "  -c, --config <文件>   指定配置文件 (默认: device-mapping.json)"
      );
      console.log("  -h, --help           显示帮助信息");
      console.log("");
      console.log("示例:");
      console.log("  node tcp-server.js --port 9000");
      console.log("  node tcp-server.js -p 9000 -c my-config.json");
      console.log("  PORT=9000 node tcp-server.js");
      process.exit(0);
    }
  }

  return { port, configFile };
}

// 配置和启动
const cmdArgs = parseCommandLineArgs();
const config = {
  port: cmdArgs.port,
  mappingConfigFile: cmdArgs.configFile,
};

const server = new TCPServer(config);

// 优雅关闭
process.on("SIGINT", () => {
  console.log("\n正在关闭服务器...");
  server.stop();
  process.exit(0);
});

process.on("SIGTERM", () => {
  server.stop();
  process.exit(0);
});

// 启动服务器
console.log("正在启动TCP服务器...");
try {
  server.start();
  console.log("服务器启动命令已执行");
} catch (error) {
  console.error("启动服务器时出错:", error);
  process.exit(1);
}

// 定时显示在线设备
setInterval(() => {
  const devices = server.getOnlineDevices();
  if (devices.length > 0) {
    console.log(`\n在线设备 (${devices.length}): ${devices.join(", ")}`);
  }
}, 30000); // 每30秒显示一次
