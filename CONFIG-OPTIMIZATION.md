# 配置优化说明

## 优化内容

TCP设备转发工具的配置已经进行了全面优化，让配置更简单、更灵活、更友好。

## 🎯 主要改进

### 1. 支持多种设备ID格式

**之前** (只支持十六进制字符串):
```json
{
  "deviceMappings": {
    "0x12345678": ["0x87654321"],
    "0x87654321": ["0x12345678"]
  }
}
```

**现在** (支持多种格式):
```json
{
  "deviceMappings": {
    "12345678": "87654321",
    "87654321": "12345678"
  }
}
```

### 2. 支持的ID格式

| 格式 | 示例 | 说明 |
|------|------|------|
| **十六进制** (推荐) | `"12345678"`, `"AABBCCDD"` | 简洁，无需0x前缀 |
| 带前缀十六进制 | `"0x12345678"`, `"0XAABBCCDD"` | 传统格式 |
| 十进制数字 | `"305419896"` | 数字字符串 |
| 数字类型 | `305419896` | JSON数字类型 |

### 3. 简化的两设备配置

**之前** (数组格式):
```json
{
  "deviceMappings": {
    "0x12345678": ["0x87654321"]
  }
}
```

**现在** (字符串格式):
```json
{
  "deviceMappings": {
    "12345678": "87654321"
  }
}
```

## 🛠️ 配置工具

### 1. 交互式配置生成器

```bash
# 生成新配置
npm run config
```

**使用流程:**
```
=== TCP设备转发配置生成器 ===

支持的设备ID格式:
  - 十六进制: 12345678, AABBCCDD
  - 带前缀: 0x12345678, 0XAABBCCDD
  - 十进制: 305419896

请输入设备A的ID: 12345678
✅ 设备A ID: 12345678

请输入设备B的ID: 87654321
✅ 设备B ID: 87654321

默认行为选项:
  1. drop - 丢弃未配置设备的数据 (推荐)
  2. broadcast - 广播给所有其他设备
请选择默认行为 (1/2, 默认1): 1

=== 配置生成完成 ===
✅ 配置文件已保存: device-mapping.json
```

### 2. 查看当前配置

```bash
# 显示当前配置
npm run config-show
```

### 3. 配置验证

服务器启动时会自动验证配置格式：
```
设备映射配置加载成功:
  映射规则数量: 2
  默认行为: drop
  映射规则:
    12345678 -> [87654321]
    87654321 -> [12345678]
```

## 📝 配置示例

### 示例1: 简化格式 (推荐)
```json
{
  "deviceMappings": {
    "12345678": "87654321",
    "87654321": "12345678"
  },
  "defaultBehavior": "drop"
}
```

### 示例2: 混合格式
```json
{
  "deviceMappings": {
    "0x12345678": "87654321",
    "305419896": "0xAABBCCDD"
  },
  "defaultBehavior": "drop"
}
```

### 示例3: 数字格式
```json
{
  "deviceMappings": {
    305419896: 2271560481,
    2271560481: 305419896
  },
  "defaultBehavior": "drop"
}
```

## 🔧 高级功能

### 1. 错误处理

配置解析时会自动处理错误：
```
解析设备ID失败: invalid_id -> 87654321 无效的设备ID格式: invalid_id
```

### 2. 格式自动转换

所有格式都会自动转换为内部的数字格式：
- `"12345678"` → `0x12345678`
- `"0x87654321"` → `0x87654321`
- `"305419896"` → `0x12345678`

### 3. 配置验证

启动时会验证：
- JSON格式是否正确
- 设备ID格式是否有效
- 映射关系是否合理

## 🚀 使用方法

### 1. 快速开始
```bash
# 生成配置
npm run config

# 启动服务器
npm start

# 运行测试
npm test
```

### 2. 手动配置
直接编辑 `device-mapping.json`:
```json
{
  "deviceMappings": {
    "YOUR_DEVICE_A_ID": "YOUR_DEVICE_B_ID",
    "YOUR_DEVICE_B_ID": "YOUR_DEVICE_A_ID"
  },
  "defaultBehavior": "drop"
}
```

### 3. 配置检查
```bash
# 检查当前配置
npm run config-show

# 启动服务器查看加载日志
npm start
```

## 💡 最佳实践

### 1. ID格式选择
- **推荐**: 使用简化十六进制格式 `"12345678"`
- **兼容**: 支持带前缀格式 `"0x12345678"`
- **避免**: 避免使用十进制格式，不够直观

### 2. 配置管理
- 使用配置生成器创建初始配置
- 定期备份配置文件
- 修改配置后重启服务器

### 3. 调试技巧
- 查看服务器启动日志确认配置加载
- 使用 `npm run config-show` 验证配置
- 运行测试验证转发功能

## 🔄 迁移指南

### 从旧格式迁移

**旧配置:**
```json
{
  "deviceMappings": {
    "0x12345678": ["0x87654321"],
    "0x87654321": ["0x12345678"]
  }
}
```

**新配置:**
```json
{
  "deviceMappings": {
    "12345678": "87654321",
    "87654321": "12345678"
  }
}
```

**迁移步骤:**
1. 备份原配置文件
2. 运行 `npm run config` 生成新配置
3. 或手动修改去掉 `0x` 前缀和数组格式
4. 重启服务器验证

## 📊 优化效果

| 方面 | 优化前 | 优化后 |
|------|--------|--------|
| 配置复杂度 | 需要0x前缀和数组 | 简单字符串 |
| 格式支持 | 仅十六进制字符串 | 多种格式 |
| 错误处理 | 基础错误提示 | 详细错误信息 |
| 配置工具 | 手动编辑 | 交互式生成器 |
| 用户体验 | 复杂 | 简单友好 |
