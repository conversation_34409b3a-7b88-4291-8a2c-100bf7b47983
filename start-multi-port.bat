@echo off
echo ===== TCP设备转发服务器 - 多端口启动脚本 =====
echo.

echo 选择启动模式:
echo   1. 默认端口 (8080)
echo   2. 双端口 (8080, 8081)
echo   3. 三端口 (8080, 8081, 8082)
echo   4. 四端口 (8080, 8081, 8082, 8083)
echo   5. 自定义端口
echo   6. 查看帮助
echo.

set /p choice="请选择 (1-6): "

if "%choice%"=="1" (
    echo 启动单端口模式: 8080
    node tcp-server.js --port 8080
) else if "%choice%"=="2" (
    echo 启动双端口模式: 8080, 8081
    node tcp-server.js --ports 8080,8081
) else if "%choice%"=="3" (
    echo 启动三端口模式: 8080, 8081, 8082
    node tcp-server.js --ports 8080,8081,8082
) else if "%choice%"=="4" (
    echo 启动四端口模式: 8080, 8081, 8082, 8083
    node tcp-server.js --ports 8080,8081,8082,8083
) else if "%choice%"=="5" (
    set /p custom_ports="请输入端口列表 (用逗号分隔): "
    echo 启动自定义端口模式: %custom_ports%
    node tcp-server.js --ports %custom_ports%
) else if "%choice%"=="6" (
    node tcp-server.js --help
    pause
) else (
    echo 无效选择，使用默认端口 8080
    node tcp-server.js --port 8080
)

echo.
echo 服务器已停止
pause
