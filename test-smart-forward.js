const net = require('net');

// 智能转发测试 - 验证第一条数据不丢失
class SmartForwardTest {
    constructor() {
        this.deviceA = null;
        this.deviceB = null;
        this.serverHost = 'localhost';
        this.serverPort = 6000;
        this.receivedMessages = [];
    }

    // 创建数据包
    createDataPacket(deviceId, data) {
        const buffer = Buffer.alloc(4 + data.length);
        buffer.writeUInt32BE(deviceId, 0);
        data.copy(buffer, 4);
        return buffer;
    }

    // 创建设备A
    createDeviceA() {
        return new Promise((resolve, reject) => {
            this.deviceA = new net.Socket();
            
            this.deviceA.connect(this.serverPort, this.serverHost, () => {
                console.log('[设备A] 已连接到服务器');
                console.log('[设备A] 注意：此时服务器还不知道我的设备ID');
                resolve();
            });

            this.deviceA.on('data', (buffer) => {
                const timestamp = new Date().toLocaleTimeString();
                console.log(`\n[设备A] ${timestamp} 收到转发数据:`);
                console.log(`  完整数据包: ${buffer.toString('hex').toUpperCase()}`);
                
                if (buffer.length >= 4) {
                    const receivedDeviceId = buffer.readUInt32BE(0);
                    const receivedData = buffer.slice(4);
                    const receivedIdHex = receivedDeviceId.toString(16).toUpperCase().padStart(8, '0');
                    
                    console.log(`  目标设备ID: ${receivedIdHex} (应该是我的ID: 12345678)`);
                    console.log(`  实际数据: ${receivedData.toString('hex').toUpperCase()}`);
                    console.log(`  ID替换${receivedDeviceId === 0x12345678 ? '正确' : '错误'}!`);
                    
                    this.receivedMessages.push({
                        device: 'A',
                        timestamp: timestamp,
                        targetId: receivedDeviceId,
                        data: receivedData,
                        correct: receivedDeviceId === 0x12345678
                    });
                }
                console.log('---');
            });

            this.deviceA.on('error', (err) => {
                console.error('[设备A] 连接错误:', err.message);
                reject(err);
            });

            this.deviceA.on('close', () => {
                console.log('[设备A] 连接关闭');
            });
        });
    }

    // 创建设备B
    createDeviceB() {
        return new Promise((resolve, reject) => {
            this.deviceB = new net.Socket();
            
            this.deviceB.connect(this.serverPort, this.serverHost, () => {
                console.log('[设备B] 已连接到服务器');
                console.log('[设备B] 注意：此时服务器还不知道我的设备ID');
                resolve();
            });

            this.deviceB.on('data', (buffer) => {
                const timestamp = new Date().toLocaleTimeString();
                console.log(`\n[设备B] ${timestamp} 收到转发数据:`);
                console.log(`  完整数据包: ${buffer.toString('hex').toUpperCase()}`);
                
                if (buffer.length >= 4) {
                    const receivedDeviceId = buffer.readUInt32BE(0);
                    const receivedData = buffer.slice(4);
                    const receivedIdHex = receivedDeviceId.toString(16).toUpperCase().padStart(8, '0');
                    
                    console.log(`  目标设备ID: ${receivedIdHex} (应该是我的ID: 87654321)`);
                    console.log(`  实际数据: ${receivedData.toString('hex').toUpperCase()}`);
                    console.log(`  ID替换${receivedDeviceId === 0x87654321 ? '正确' : '错误'}!`);
                    
                    this.receivedMessages.push({
                        device: 'B',
                        timestamp: timestamp,
                        targetId: receivedDeviceId,
                        data: receivedData,
                        correct: receivedDeviceId === 0x87654321
                    });
                }
                console.log('---');
            });

            this.deviceB.on('error', (err) => {
                console.error('[设备B] 连接错误:', err.message);
                reject(err);
            });

            this.deviceB.on('close', () => {
                console.log('[设备B] 连接关闭');
            });
        });
    }

    // 设备A发送数据
    deviceASendData(data, description = '') {
        if (!this.deviceA) {
            console.error('[设备A] 未连接');
            return false;
        }

        const packet = this.createDataPacket(0x12345678, data);
        this.deviceA.write(packet);
        
        const timestamp = new Date().toLocaleTimeString();
        console.log(`\n[设备A] ${timestamp} 发送数据 ${description}:`);
        console.log(`  源设备ID: 12345678`);
        console.log(`  发送数据包: ${packet.toString('hex').toUpperCase()}`);
        console.log(`  实际数据: ${data.toString('hex').toUpperCase()}`);
        console.log('---');
        return true;
    }

    // 设备B发送数据
    deviceBSendData(data, description = '') {
        if (!this.deviceB) {
            console.error('[设备B] 未连接');
            return false;
        }

        const packet = this.createDataPacket(0x87654321, data);
        this.deviceB.write(packet);
        
        const timestamp = new Date().toLocaleTimeString();
        console.log(`\n[设备B] ${timestamp} 发送数据 ${description}:`);
        console.log(`  源设备ID: 87654321`);
        console.log(`  发送数据包: ${packet.toString('hex').toUpperCase()}`);
        console.log(`  实际数据: ${data.toString('hex').toUpperCase()}`);
        console.log('---');
        return true;
    }

    // 显示测试结果
    showResults() {
        console.log('\n=== 测试结果统计 ===');
        console.log(`总接收消息数: ${this.receivedMessages.length}`);
        
        const correctMappings = this.receivedMessages.filter(msg => msg.correct).length;
        const incorrectMappings = this.receivedMessages.filter(msg => !msg.correct).length;
        
        console.log(`正确ID映射: ${correctMappings}`);
        console.log(`错误ID映射: ${incorrectMappings}`);
        
        console.log('\n详细消息列表:');
        this.receivedMessages.forEach((msg, index) => {
            console.log(`${index + 1}. [设备${msg.device}] ${msg.timestamp}`);
            console.log(`   目标ID: ${msg.targetId.toString(16).toUpperCase().padStart(8, '0')}`);
            console.log(`   数据: ${msg.data.toString('hex').toUpperCase()}`);
            console.log(`   映射: ${msg.correct ? '正确' : '错误'}`);
        });
        
        console.log('\n=== 关键验证点 ===');
        console.log('✓ 第一条数据是否成功转发？', this.receivedMessages.length > 0 ? '是' : '否');
        console.log('✓ ID替换是否正确？', correctMappings === this.receivedMessages.length ? '是' : '否');
        console.log('✓ 智能转发是否工作？', this.receivedMessages.length > 0 ? '是' : '否');
    }

    // 断开连接
    disconnect() {
        if (this.deviceA) {
            this.deviceA.destroy();
            this.deviceA = null;
        }
        if (this.deviceB) {
            this.deviceB.destroy();
            this.deviceB = null;
        }
    }

    // 运行测试
    async runTest() {
        console.log('=== 智能转发测试 ===\n');
        console.log('测试目标：验证第一条数据不会丢失');
        console.log('场景：两台设备连接后，设备A立即发送数据');
        console.log('预期：服务器自动识别设备B并转发数据\n');

        try {
            // 1. 连接设备
            console.log('1. 连接设备到服务器...\n');
            await this.createDeviceA();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await this.createDeviceB();
            await new Promise(resolve => setTimeout(resolve, 1000));

            console.log('\n2. 立即发送第一条数据（关键测试）...\n');

            // 2. 设备A立即发送第一条数据
            setTimeout(() => {
                const firstData = Buffer.from([0x01, 0x02, 0x03, 0x04]);
                this.deviceASendData(firstData, '(第一条数据 - 关键测试)');
            }, 500);

            // 3. 等待一下，然后设备B也发送数据
            setTimeout(() => {
                const secondData = Buffer.from([0x05, 0x06, 0x07, 0x08]);
                this.deviceBSendData(secondData, '(设备B的第一条数据)');
            }, 2000);

            // 4. 继续测试双向通信
            setTimeout(() => {
                const thirdData = Buffer.from([0x09, 0x0A, 0x0B]);
                this.deviceASendData(thirdData, '(设备A的第二条数据)');
            }, 3500);

            setTimeout(() => {
                const fourthData = Buffer.from([0x0C, 0x0D, 0x0E]);
                this.deviceBSendData(fourthData, '(设备B的第二条数据)');
            }, 5000);

            // 5. 7秒后结束测试
            setTimeout(() => {
                console.log('\n3. 测试完成，分析结果...\n');
                this.showResults();
                this.disconnect();
                
                console.log('\n=== 智能转发测试结束 ===');
                process.exit(0);
            }, 7000);

        } catch (error) {
            console.error('测试失败:', error);
            this.disconnect();
            process.exit(1);
        }
    }
}

// 运行测试
if (require.main === module) {
    const test = new SmartForwardTest();
    test.runTest();
}

module.exports = SmartForwardTest;
