# TCP 设备数据转发服务器

一个基于设备 ID 的 TCP 数据转发服务器，多个设备连接到服务器，服务器根据设备 ID 进行数据转发。

## 功能特性

- TCP 服务器，支持多设备连接
- 基于设备 ID 的数据包解析
- 设备注册和管理
- 数据转发（广播或指定设备）
- 实时连接状态监控
- 优雅关闭处理

## 数据包格式说明

根据交互流程图，数据包格式如下：

```
| 字段 | 长度 | 说明 |
|------|------|------|
| 设备ID | 4字节 | 用于标识设备，如IDA、IDB、IDM、IDN等 |
| 数据 | 可变长度 | 实际要转发的数据内容 |
```

## 工作流程

1. **设备连接**: 多个设备（A、B、M、N 等）通过 TCP 连接到服务器
2. **设备注册**: 设备发送第一个数据包时，服务器解析设备 ID 并注册设备
3. **数据转发**: 服务器接收到数据后，根据设备 ID 进行转发
   - 可以广播给所有其他设备
   - 也可以转发给指定的目标设备
4. **连接管理**: 服务器维护设备连接状态，处理设备上线/下线

## 安装和使用

1. 确保已安装 Node.js
2. 克隆或下载此项目
3. 配置参数（可选）
4. 启动服务

### 启动方式

```bash
# 启动TCP服务器
npm start
# 或
node tcp-server.js

# 运行测试客户端
npm test
# 或
node test-devices.js

# 使用环境变量配置端口
PORT=9000 node tcp-server.js
```

### 配置参数

可以通过以下方式配置：

1. **环境变量**:

   - `PORT`: 服务器监听端口（默认: 8080）

2. **修改代码中的 config 对象**

## 使用示例

### 启动服务器

```bash
node tcp-server.js
```

输出示例：

```
TCP服务器启动成功
监听端口: 8080
等待设备连接...
---
[1] 新连接: 127.0.0.1:54321
[1] 收到数据: 8 字节
[1] 解析数据包:
  源设备ID: 12345678
  数据长度: 4 字节
  数据内容: 11223344
[1] 设备注册成功: ID=12345678
[1] 当前在线设备数: 1
数据已转发给 0 个设备
```

### 测试多设备交互

```bash
# 在另一个终端运行测试客户端
node test-devices.js
```

这将模拟 4 个设备（A、B、M、N）连接到服务器并进行数据交互。

## 文件说明

### 核心文件

- **`tcp-server.js`** - 主 TCP 服务器实现
- **`device-mapping.json`** - 设备映射配置文件
- **`package.json`** - 项目配置文件

### 测试文件

- **`test-devices.js`** - 基础设备连接测试
- **`test-id-mapping.js`** - ID 映射功能测试
- **`test-mapping-config.js`** - 映射配置功能测试

### 打包文件

- **`build.bat`** - Windows 平台打包脚本
- **`build-all.bat`** - 跨平台打包脚本

### 文档文件

- **`README.md`** - 项目主要说明
- **`MAPPING-CONFIG.md`** - 映射配置详细说明
- **`BINARY-USAGE.md`** - 二进制打包和使用说明

## 扩展功能

可以根据需要扩展以下功能：

1. **指定目标转发**: 修改转发逻辑，根据数据内容决定转发目标
2. **数据过滤**: 添加数据过滤规则
3. **持久化**: 添加数据库存储设备信息和消息历史
4. **Web 界面**: 添加 Web 管理界面监控设备状态
5. **认证**: 添加设备认证机制

## 注意事项

1. 确保防火墙允许指定端口的连接
2. 设备 ID 应该唯一，避免冲突
3. 数据包格式必须符合协议要求（4 字节设备 ID + 数据）
4. 使用 Ctrl+C 优雅关闭服务器

## 许可证

MIT License
