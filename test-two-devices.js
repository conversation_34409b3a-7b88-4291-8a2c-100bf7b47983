const net = require('net');

// 两设备互发测试
class TwoDeviceTest {
    constructor() {
        this.deviceA = null;
        this.deviceB = null;
        this.serverHost = 'localhost';
        this.serverPort = 6000;
    }

    // 创建数据包
    createDataPacket(deviceId, data) {
        const buffer = Buffer.alloc(4 + data.length);
        buffer.writeUInt32BE(deviceId, 0);
        data.copy(buffer, 4);
        return buffer;
    }

    // 创建设备A
    createDeviceA() {
        return new Promise((resolve, reject) => {
            this.deviceA = new net.Socket();
            
            this.deviceA.connect(this.serverPort, this.serverHost, () => {
                console.log('[设备A] 已连接到服务器 (ID: 12345678)');
                resolve();
            });

            this.deviceA.on('data', (buffer) => {
                console.log(`\n[设备A] 收到转发数据:`);
                console.log(`  完整数据包: ${buffer.toString('hex').toUpperCase()}`);
                
                if (buffer.length >= 4) {
                    const receivedDeviceId = buffer.readUInt32BE(0);
                    const receivedData = buffer.slice(4);
                    const receivedIdHex = receivedDeviceId.toString(16).toUpperCase().padStart(8, '0');
                    
                    console.log(`  目标设备ID: ${receivedIdHex} (应该是我的ID: 12345678)`);
                    console.log(`  实际数据: ${receivedData.toString('hex').toUpperCase()}`);
                    console.log(`  ID替换${receivedDeviceId === 0x12345678 ? '正确' : '错误'}!`);
                }
                console.log('---');
            });

            this.deviceA.on('error', (err) => {
                console.error('[设备A] 连接错误:', err.message);
                reject(err);
            });

            this.deviceA.on('close', () => {
                console.log('[设备A] 连接关闭');
            });
        });
    }

    // 创建设备B
    createDeviceB() {
        return new Promise((resolve, reject) => {
            this.deviceB = new net.Socket();
            
            this.deviceB.connect(this.serverPort, this.serverHost, () => {
                console.log('[设备B] 已连接到服务器 (ID: 87654321)');
                resolve();
            });

            this.deviceB.on('data', (buffer) => {
                console.log(`\n[设备B] 收到转发数据:`);
                console.log(`  完整数据包: ${buffer.toString('hex').toUpperCase()}`);
                
                if (buffer.length >= 4) {
                    const receivedDeviceId = buffer.readUInt32BE(0);
                    const receivedData = buffer.slice(4);
                    const receivedIdHex = receivedDeviceId.toString(16).toUpperCase().padStart(8, '0');
                    
                    console.log(`  目标设备ID: ${receivedIdHex} (应该是我的ID: 87654321)`);
                    console.log(`  实际数据: ${receivedData.toString('hex').toUpperCase()}`);
                    console.log(`  ID替换${receivedDeviceId === 0x87654321 ? '正确' : '错误'}!`);
                }
                console.log('---');
            });

            this.deviceB.on('error', (err) => {
                console.error('[设备B] 连接错误:', err.message);
                reject(err);
            });

            this.deviceB.on('close', () => {
                console.log('[设备B] 连接关闭');
            });
        });
    }

    // 设备A发送数据
    deviceASendData(data, description = '') {
        if (!this.deviceA) {
            console.error('[设备A] 未连接');
            return false;
        }

        const packet = this.createDataPacket(0x12345678, data);
        this.deviceA.write(packet);
        
        console.log(`\n[设备A] 发送数据 ${description}:`);
        console.log(`  源设备ID: 12345678`);
        console.log(`  发送数据包: ${packet.toString('hex').toUpperCase()}`);
        console.log(`  实际数据: ${data.toString('hex').toUpperCase()}`);
        console.log('---');
        return true;
    }

    // 设备B发送数据
    deviceBSendData(data, description = '') {
        if (!this.deviceB) {
            console.error('[设备B] 未连接');
            return false;
        }

        const packet = this.createDataPacket(0x87654321, data);
        this.deviceB.write(packet);
        
        console.log(`\n[设备B] 发送数据 ${description}:`);
        console.log(`  源设备ID: 87654321`);
        console.log(`  发送数据包: ${packet.toString('hex').toUpperCase()}`);
        console.log(`  实际数据: ${data.toString('hex').toUpperCase()}`);
        console.log('---');
        return true;
    }

    // 断开连接
    disconnect() {
        if (this.deviceA) {
            this.deviceA.destroy();
            this.deviceA = null;
        }
        if (this.deviceB) {
            this.deviceB.destroy();
            this.deviceB = null;
        }
    }

    // 运行测试
    async runTest() {
        console.log('=== 两设备互发测试 ===\n');
        console.log('配置说明:');
        console.log('  设备A (12345678) <-> 设备B (87654321)');
        console.log('  双向数据转发，ID自动替换\n');

        try {
            // 连接设备
            console.log('1. 连接设备到服务器...\n');
            await this.createDeviceA();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await this.createDeviceB();
            await new Promise(resolve => setTimeout(resolve, 1000));

            console.log('\n2. 开始双向数据测试...\n');

            // 测试1: 设备A发送数据给设备B
            setTimeout(() => {
                const testData1 = Buffer.from([0x11, 0x22, 0x33, 0x44]);
                this.deviceASendData(testData1, '(A->B)');
            }, 2000);

            // 测试2: 设备B发送数据给设备A
            setTimeout(() => {
                const testData2 = Buffer.from([0x55, 0x66, 0x77, 0x88]);
                this.deviceBSendData(testData2, '(B->A)');
            }, 4000);

            // 测试3: 设备A再次发送
            setTimeout(() => {
                const testData3 = Buffer.from([0xAA, 0xBB, 0xCC]);
                this.deviceASendData(testData3, '(A->B)');
            }, 6000);

            // 测试4: 设备B再次发送
            setTimeout(() => {
                const testData4 = Buffer.from([0xFF, 0xEE, 0xDD]);
                this.deviceBSendData(testData4, '(B->A)');
            }, 8000);

            // 10秒后结束测试
            setTimeout(() => {
                console.log('\n3. 测试完成，断开连接...\n');
                this.disconnect();
                
                console.log('=== 两设备互发测试结束 ===');
                console.log('预期结果：');
                console.log('- 设备A发送的数据应该被设备B收到，ID替换为87654321');
                console.log('- 设备B发送的数据应该被设备A收到，ID替换为12345678');
                process.exit(0);
            }, 10000);

        } catch (error) {
            console.error('测试失败:', error);
            this.disconnect();
            process.exit(1);
        }
    }
}

// 运行测试
if (require.main === module) {
    const test = new TwoDeviceTest();
    test.runTest();
}

module.exports = TwoDeviceTest;
