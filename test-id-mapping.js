const net = require('net');

// 演示ID映射转发的测试客户端
class TestDevice {
    constructor(deviceId, name, serverHost = 'localhost', serverPort = 8080) {
        this.deviceId = deviceId;
        this.deviceIdHex = deviceId.toString(16).toUpperCase().padStart(8, '0');
        this.name = name;
        this.serverHost = serverHost;
        this.serverPort = serverPort;
        this.socket = null;
        this.connected = false;
    }

    // 创建数据包
    createDataPacket(data) {
        const buffer = Buffer.alloc(4 + data.length);
        buffer.writeUInt32BE(this.deviceId, 0);
        data.copy(buffer, 4);
        return buffer;
    }

    // 连接到服务器
    connect() {
        return new Promise((resolve, reject) => {
            this.socket = new net.Socket();
            
            this.socket.connect(this.serverPort, this.serverHost, () => {
                this.connected = true;
                console.log(`[${this.name}] 设备ID ${this.deviceIdHex} 已连接到服务器`);
                resolve();
            });

            this.socket.on('data', (buffer) => {
                console.log(`\n[${this.name}] 收到转发数据:`);
                console.log(`  完整数据包: ${buffer.toString('hex').toUpperCase()}`);
                
                // 解析收到的数据包
                if (buffer.length >= 4) {
                    const receivedDeviceId = buffer.readUInt32BE(0);
                    const receivedDeviceIdHex = receivedDeviceId.toString(16).toUpperCase().padStart(8, '0');
                    const data = buffer.slice(4);
                    
                    console.log(`  目标设备ID: ${receivedDeviceIdHex} (应该是我的ID: ${this.deviceIdHex})`);
                    console.log(`  实际数据: ${data.toString('hex').toUpperCase()}`);
                    console.log(`  ID映射${receivedDeviceId === this.deviceId ? '正确' : '错误'}!`);
                }
                console.log('---');
            });

            this.socket.on('error', (err) => {
                console.error(`[${this.name}] 连接错误:`, err.message);
                this.connected = false;
                reject(err);
            });

            this.socket.on('close', () => {
                console.log(`[${this.name}] 连接已关闭`);
                this.connected = false;
            });
        });
    }

    // 发送数据
    sendData(data, description = '') {
        if (!this.connected || !this.socket) {
            console.error(`[${this.name}] 未连接到服务器`);
            return false;
        }

        try {
            const packet = this.createDataPacket(data);
            this.socket.write(packet);
            
            console.log(`\n[${this.name}] 发送数据 ${description}:`);
            console.log(`  源设备ID: ${this.deviceIdHex}`);
            console.log(`  发送数据包: ${packet.toString('hex').toUpperCase()}`);
            console.log(`  实际数据: ${data.toString('hex').toUpperCase()}`);
            console.log('---');
            return true;
        } catch (error) {
            console.error(`[${this.name}] 发送数据失败:`, error);
            return false;
        }
    }

    // 断开连接
    disconnect() {
        if (this.socket) {
            this.socket.destroy();
            this.socket = null;
            this.connected = false;
        }
    }
}

// ID映射转发测试
async function testIdMapping() {
    console.log('=== TCP设备ID映射转发测试 ===\n');

    // 创建测试设备
    const deviceA = new TestDevice(0x12345678, '设备A'); // IDA
    const deviceB = new TestDevice(0x87654321, '设备B'); // IDB
    const deviceM = new TestDevice(0xAABBCCDD, '设备M'); // IDM
    const deviceN = new TestDevice(0xDDCCBBAA, '设备N'); // IDN

    try {
        // 连接所有设备
        console.log('1. 连接设备到服务器...\n');
        await deviceA.connect();
        await new Promise(resolve => setTimeout(resolve, 500));
        
        await deviceB.connect();
        await new Promise(resolve => setTimeout(resolve, 500));
        
        await deviceM.connect();
        await new Promise(resolve => setTimeout(resolve, 500));
        
        await deviceN.connect();
        await new Promise(resolve => setTimeout(resolve, 1000));

        console.log('\n2. 开始ID映射转发测试...\n');

        // 测试1: 设备A发送数据，观察其他设备收到的ID是否正确替换
        setTimeout(() => {
            const testData1 = Buffer.from([0x11, 0x22, 0x33, 0x44]);
            deviceA.sendData(testData1, '(测试数据1)');
        }, 2000);

        // 测试2: 设备B发送数据
        setTimeout(() => {
            const testData2 = Buffer.from([0x55, 0x66, 0x77, 0x88, 0x99]);
            deviceB.sendData(testData2, '(测试数据2)');
        }, 4000);

        // 测试3: 设备M发送数据
        setTimeout(() => {
            const testData3 = Buffer.from([0xAA, 0xBB, 0xCC]);
            deviceM.sendData(testData3, '(测试数据3)');
        }, 6000);

        // 测试4: 设备N发送数据
        setTimeout(() => {
            const testData4 = Buffer.from([0xFF, 0xEE, 0xDD, 0xCC, 0xBB, 0xAA, 0x99]);
            deviceN.sendData(testData4, '(测试数据4)');
        }, 8000);

        // 12秒后断开所有连接
        setTimeout(() => {
            console.log('\n3. 测试完成，断开所有连接...\n');
            deviceA.disconnect();
            deviceB.disconnect();
            deviceM.disconnect();
            deviceN.disconnect();
            
            console.log('=== ID映射转发测试结束 ===');
            console.log('预期结果：每个设备收到的数据包前4字节应该是自己的设备ID');
            process.exit(0);
        }, 12000);

    } catch (error) {
        console.error('测试失败:', error);
        process.exit(1);
    }
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
    testIdMapping();
}

module.exports = TestDevice;
