# TCP转发工具 - 二进制版本使用说明

## 打包方法

### 方法1：使用 pkg（推荐）

#### 安装 pkg
```bash
npm install -g pkg
```

#### 打包命令

**仅打包 Windows 版本：**
```bash
npm run build
```
或者直接运行：
```bash
build.bat
```

**打包所有平台版本：**
```bash
npm run build-all
```
或者直接运行：
```bash
build-all.bat
```

#### 手动打包命令
```bash
# Windows 64位
pkg tcp-server.js --targets node18-win-x64 --output dist/tcp-server.exe

# Linux 64位
pkg tcp-server.js --targets node18-linux-x64 --output dist/tcp-server-linux

# macOS 64位
pkg tcp-server.js --targets node18-macos-x64 --output dist/tcp-server-macos

# 所有平台
pkg tcp-server.js --targets node18-win-x64,node18-linux-x64,node18-macos-x64 --out-path dist
```

## 二进制文件使用方法

### 1. 部署
1. 将打包后的可执行文件复制到目标机器
2. 将 `device-mapping.json` 配置文件放在可执行文件同一目录
3. 确保配置文件权限正确（可读写）

### 2. 运行

**Windows:**
```cmd
tcp-server.exe
```

**Linux/macOS:**
```bash
chmod +x tcp-server
./tcp-server
```

### 3. 配置

修改 `device-mapping.json` 文件来配置设备映射规则：

```json
{
  "deviceMappings": {
    "0x12345678": ["0x87654321", "0xAABBCCDD"],
    "0x87654321": ["0x12345678"],
    "0xAABBCCDD": ["0xDDCCBBAA"],
    "0xDDCCBBAA": ["0x12345678", "0x87654321", "0xAABBCCDD"]
  },
  "defaultBehavior": "broadcast"
}
```

### 4. 环境变量

可以通过环境变量配置端口：

**Windows:**
```cmd
set PORT=9090
tcp-server.exe
```

**Linux/macOS:**
```bash
PORT=9090 ./tcp-server
```

### 5. 服务化部署

#### Windows 服务
使用 NSSM 或类似工具将可执行文件注册为 Windows 服务：

```cmd
# 下载 NSSM
# 安装服务
nssm install TCPForwarder "C:\path\to\tcp-server.exe"
nssm set TCPForwarder AppDirectory "C:\path\to\"
nssm start TCPForwarder
```

#### Linux systemd 服务
创建服务文件 `/etc/systemd/system/tcp-forwarder.service`：

```ini
[Unit]
Description=TCP Device Forwarder
After=network.target

[Service]
Type=simple
User=tcp-forwarder
WorkingDirectory=/opt/tcp-forwarder
ExecStart=/opt/tcp-forwarder/tcp-server
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

启动服务：
```bash
sudo systemctl enable tcp-forwarder
sudo systemctl start tcp-forwarder
```

## 方法2：使用 nexe

### 安装和使用
```bash
npm install -g nexe
nexe tcp-server.js --target windows-x64-18.15.0 --output tcp-server.exe
```

## 方法3：使用 node-sea（Node.js 20+）

### 创建配置文件
```json
{
  "main": "tcp-server.js",
  "output": "tcp-server.blob",
  "disableExperimentalSEAWarning": true
}
```

### 打包命令
```bash
node --experimental-sea-config sea-config.json
node -e "require('fs').copyFileSync(process.execPath, 'tcp-server.exe')"
npx postject tcp-server.exe NODE_SEA_BLOB tcp-server.blob --sentinel-fuse NODE_SEA_FUSE_fce680ab2cc467b6e072b8b5df1996b2
```

## 优势对比

| 方法 | 优势 | 劣势 |
|------|------|------|
| pkg | 简单易用，支持多平台 | 文件较大 |
| nexe | 文件较小，启动快 | 配置复杂 |
| node-sea | 官方支持，安全性好 | 需要 Node.js 20+ |

## 注意事项

1. **配置文件路径**: 二进制文件会在当前工作目录查找 `device-mapping.json`
2. **权限问题**: Linux/macOS 需要执行权限
3. **防火墙**: 确保目标端口（默认8080）未被防火墙阻止
4. **依赖**: 二进制文件包含了所有依赖，无需安装 Node.js
5. **更新**: 修改配置后无需重新打包，只需重启程序

## 测试二进制文件

使用测试客户端验证功能：
```bash
# 如果有 Node.js 环境
node test-mapping-config.js

# 或者使用 telnet 等工具手动测试
telnet localhost 8080
```

## 故障排除

1. **端口占用**: 修改环境变量 PORT 或配置文件
2. **权限不足**: 使用管理员权限运行或修改文件权限
3. **配置错误**: 检查 JSON 格式是否正确
4. **网络问题**: 检查防火墙和网络连接
