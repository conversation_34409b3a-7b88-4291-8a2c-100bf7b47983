const fs = require('fs');
const readline = require('readline');

// 配置生成器
class ConfigGenerator {
    constructor() {
        this.rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
    }

    // 询问用户输入
    question(prompt) {
        return new Promise((resolve) => {
            this.rl.question(prompt, resolve);
        });
    }

    // 验证设备ID格式
    validateDeviceId(input) {
        const str = input.toString().trim();
        
        // 支持多种格式
        if (str.startsWith('0x') || str.startsWith('0X')) {
            const hex = str.slice(2);
            if (/^[0-9A-Fa-f]{1,8}$/.test(hex)) {
                return parseInt(str, 16);
            }
        }
        
        if (/^\d+$/.test(str)) {
            const num = parseInt(str, 10);
            if (num >= 0 && num <= 0xFFFFFFFF) {
                return num;
            }
        }
        
        if (/^[0-9A-Fa-f]{1,8}$/.test(str)) {
            return parseInt(str, 16);
        }
        
        return null;
    }

    // 格式化设备ID为十六进制字符串
    formatDeviceId(deviceId) {
        return deviceId.toString(16).toUpperCase().padStart(8, '0');
    }

    // 生成配置文件
    async generateConfig() {
        console.log('=== TCP设备转发配置生成器 ===\n');
        console.log('支持的设备ID格式:');
        console.log('  - 十六进制: 12345678, AABBCCDD');
        console.log('  - 带前缀: 0x12345678, 0XAABBCCDD');
        console.log('  - 十进制: 305419896');
        console.log('');

        try {
            // 获取设备A的ID
            let deviceAId = null;
            while (!deviceAId) {
                const input = await this.question('请输入设备A的ID: ');
                deviceAId = this.validateDeviceId(input);
                if (!deviceAId) {
                    console.log('❌ 无效的设备ID格式，请重新输入');
                } else {
                    console.log(`✅ 设备A ID: ${this.formatDeviceId(deviceAId)}`);
                }
            }

            // 获取设备B的ID
            let deviceBId = null;
            while (!deviceBId) {
                const input = await this.question('请输入设备B的ID: ');
                deviceBId = this.validateDeviceId(input);
                if (!deviceBId) {
                    console.log('❌ 无效的设备ID格式，请重新输入');
                } else if (deviceBId === deviceAId) {
                    console.log('❌ 设备B的ID不能与设备A相同，请重新输入');
                    deviceBId = null;
                } else {
                    console.log(`✅ 设备B ID: ${this.formatDeviceId(deviceBId)}`);
                }
            }

            // 选择默认行为
            console.log('\n默认行为选项:');
            console.log('  1. drop - 丢弃未配置设备的数据 (推荐)');
            console.log('  2. broadcast - 广播给所有其他设备');
            
            let defaultBehavior = 'drop';
            const behaviorChoice = await this.question('请选择默认行为 (1/2, 默认1): ');
            if (behaviorChoice === '2') {
                defaultBehavior = 'broadcast';
            }

            // 生成配置对象
            const config = {
                deviceMappings: {},
                defaultBehavior: defaultBehavior,
                description: {
                    "生成时间": new Date().toLocaleString(),
                    "设备A": `${this.formatDeviceId(deviceAId)} - 数据转发给设备B`,
                    "设备B": `${this.formatDeviceId(deviceBId)} - 数据转发给设备A`,
                    "默认行为": defaultBehavior === 'drop' ? '丢弃未配置设备的数据' : '广播给所有其他设备',
                    "工作模式": "双设备互发模式"
                }
            };

            // 使用简化格式 (不带0x前缀)
            const deviceAHex = this.formatDeviceId(deviceAId);
            const deviceBHex = this.formatDeviceId(deviceBId);
            
            config.deviceMappings[deviceAHex] = deviceBHex;
            config.deviceMappings[deviceBHex] = deviceAHex;

            // 选择输出文件名
            const defaultFilename = 'device-mapping.json';
            const filename = await this.question(`请输入配置文件名 (默认: ${defaultFilename}): `) || defaultFilename;

            // 写入文件
            fs.writeFileSync(filename, JSON.stringify(config, null, 2), 'utf8');

            console.log('\n=== 配置生成完成 ===');
            console.log(`✅ 配置文件已保存: ${filename}`);
            console.log('\n配置内容:');
            console.log(`  设备A (${deviceAHex}) ↔ 设备B (${deviceBHex})`);
            console.log(`  默认行为: ${defaultBehavior}`);
            console.log('\n使用方法:');
            console.log('  1. npm start  # 启动服务器');
            console.log('  2. npm test   # 运行测试');

        } catch (error) {
            console.error('生成配置时出错:', error.message);
        } finally {
            this.rl.close();
        }
    }

    // 显示现有配置
    showExistingConfig() {
        const filename = 'device-mapping.json';
        if (fs.existsSync(filename)) {
            console.log('\n=== 当前配置 ===');
            try {
                const config = JSON.parse(fs.readFileSync(filename, 'utf8'));
                console.log('设备映射:');
                for (const [sourceId, targetId] of Object.entries(config.deviceMappings || {})) {
                    console.log(`  ${sourceId} → ${targetId}`);
                }
                console.log(`默认行为: ${config.defaultBehavior || 'broadcast'}`);
            } catch (error) {
                console.log('❌ 配置文件格式错误');
            }
        } else {
            console.log('❌ 未找到配置文件');
        }
    }
}

// 命令行参数处理
async function main() {
    const generator = new ConfigGenerator();
    
    const args = process.argv.slice(2);
    
    if (args.includes('--show') || args.includes('-s')) {
        generator.showExistingConfig();
        generator.rl.close();
        return;
    }
    
    if (args.includes('--help') || args.includes('-h')) {
        console.log('TCP设备转发配置生成器');
        console.log('');
        console.log('用法:');
        console.log('  node config-generator.js        # 生成新配置');
        console.log('  node config-generator.js -s     # 显示当前配置');
        console.log('  node config-generator.js -h     # 显示帮助');
        console.log('');
        console.log('支持的设备ID格式:');
        console.log('  - 十六进制: 12345678, AABBCCDD');
        console.log('  - 带前缀: 0x12345678, 0XAABBCCDD');
        console.log('  - 十进制: 305419896');
        generator.rl.close();
        return;
    }
    
    await generator.generateConfig();
}

if (require.main === module) {
    main();
}

module.exports = ConfigGenerator;
