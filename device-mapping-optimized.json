{"deviceMappings": {"12345678": "87654321", "87654321": "12345678"}, "defaultBehavior": "drop", "description": {"支持的ID格式": ["十六进制 (推荐): 12345678, AABBCCDD", "带前缀十六进制: 0x12345678, 0XAABBCCDD", "十进制数字: 305419896", "数字类型: 305419896"], "配置说明": {"deviceMappings": "设备ID映射，支持多种格式", "简化配置": "两设备模式下，值可以是字符串而不是数组", "12345678": "设备A (简化的十六进制格式)", "87654321": "设备B (简化的十六进制格式)"}, "优势": ["无需0x前缀，更简洁", "支持多种格式，更灵活", "自动格式转换，更智能", "配置错误提示，更友好"]}}