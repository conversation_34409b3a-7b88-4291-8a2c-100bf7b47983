# 设备映射配置说明

## 概述

TCP服务器现在支持基于配置文件的设备ID映射转发功能。你可以通过 `device-mapping.json` 配置文件指定某个设备ID的数据应该转发到哪些特定的目标设备ID。

## 配置文件格式

### device-mapping.json

```json
{
  "deviceMappings": {
    "0x12345678": ["0x87654321", "0xAABBCCDD"],
    "0x87654321": ["0x12345678"],
    "0xAABBCCDD": ["0xDDCCBBAA"],
    "0xDDCCBBAA": ["0x12345678", "0x87654321", "0xAABBCCDD"]
  },
  "defaultBehavior": "broadcast"
}
```

### 配置说明

- **deviceMappings**: 设备映射规则
  - Key: 源设备ID (十六进制字符串格式，如 "0x12345678")
  - Value: 目标设备ID数组 (十六进制字符串数组)

- **defaultBehavior**: 默认行为
  - `"broadcast"`: 对于没有配置映射规则的设备，广播给所有其他设备
  - `"drop"`: 对于没有配置映射规则的设备，丢弃数据

## 工作原理

### 1. 服务器启动时
- 自动加载 `device-mapping.json` 配置文件
- 解析映射规则并显示配置信息
- 如果配置文件不存在，使用默认广播模式

### 2. 设备连接时
- 记录设备的详细信息（ID、地址、端口、连接时间）
- 显示该设备的转发规则

### 3. 数据转发时
- 检查源设备ID是否有特定的映射规则
- 如果有映射规则：按照配置转发给指定的目标设备
- 如果没有映射规则：使用默认行为（广播或丢弃）

## 示例场景

### 配置示例
```json
{
  "deviceMappings": {
    "0x12345678": ["0x87654321", "0xAABBCCDD"],
    "0x87654321": ["0x12345678"],
    "0xAABBCCDD": ["0xDDCCBBAA"],
    "0xDDCCBBAA": ["0x12345678", "0x87654321", "0xAABBCCDD"]
  },
  "defaultBehavior": "broadcast"
}
```

### 转发行为

1. **设备A (0x12345678) 发送数据**:
   - ✅ 转发给设备B (0x87654321)
   - ✅ 转发给设备M (0xAABBCCDD)
   - ❌ 不转发给设备N (0xDDCCBBAA)

2. **设备B (0x87654321) 发送数据**:
   - ✅ 转发给设备A (0x12345678)
   - ❌ 不转发给设备M和N

3. **设备M (0xAABBCCDD) 发送数据**:
   - ✅ 转发给设备N (0xDDCCBBAA)
   - ❌ 不转发给设备A和B

4. **设备N (0xDDCCBBAA) 发送数据**:
   - ✅ 转发给设备A (0x12345678)
   - ✅ 转发给设备B (0x87654321)
   - ✅ 转发给设备M (0xAABBCCDD)

5. **未配置的设备发送数据**:
   - 使用默认行为（本例中为广播给所有其他设备）

## 服务器日志示例

### 启动时的配置加载
```
设备映射配置加载成功:
  映射规则数量: 4
  默认行为: broadcast
  映射规则:
    12345678 -> [87654321, AABBCCDD]
    87654321 -> [12345678]
    AABBCCDD -> [DDCCBBAA]
    DDCCBBAA -> [12345678, 87654321, AABBCCDD]
```

### 设备连接时
```
[1] 设备注册成功: ID=12345678
  地址: 127.0.0.1:54321
  连接时间: 2024-08-01 10:30:15
[1] 当前在线设备数: 1
  转发规则: 12345678 -> [87654321, AABBCCDD]
```

### 数据转发时
```
使用映射规则转发: 12345678 -> [87654321, AABBCCDD]
数据转发成功: 12345678 -> 87654321
  原始数据: 11223344
  转发数据包: 8765432111223344
  ID替换: 12345678 -> 87654321
数据转发成功: 12345678 -> AABBCCDD
  原始数据: 11223344
  转发数据包: AABBCCDD11223344
  ID替换: 12345678 -> AABBCCDD
数据转发完成，成功转发给 2 个设备
```

## 测试方法

### 1. 启动服务器
```bash
npm start
```

### 2. 运行映射配置测试
```bash
npm run test-config
```

### 3. 自定义配置测试
1. 修改 `device-mapping.json` 文件
2. 重启服务器
3. 运行测试观察转发行为

## 配置文件管理

### 动态重载（未来功能）
可以考虑添加以下功能：
- 监听配置文件变化，自动重载
- 提供API接口动态修改映射规则
- 支持多个配置文件

### 配置验证
服务器会自动验证配置文件：
- 检查JSON格式是否正确
- 验证设备ID格式是否有效
- 处理配置加载错误

## 应用场景

1. **CAN总线网关**: 特定CAN节点的数据只转发给相关的目标节点
2. **设备分组通信**: 将设备分组，组内设备互相通信
3. **数据过滤**: 只转发特定设备的数据给指定的监控设备
4. **负载均衡**: 将数据分发给特定的处理设备
5. **安全隔离**: 限制设备间的通信路径

## 注意事项

1. 设备ID必须使用十六进制格式（如 "0x12345678"）
2. 目标设备必须在线才能接收转发的数据
3. 配置文件修改后需要重启服务器才能生效
4. 映射规则支持一对多转发
5. 如果目标设备不在线，会跳过转发并记录日志
