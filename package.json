{"name": "tcp-util", "version": "1.0.0", "description": "TCP server for device data forwarding based on device ID", "main": "tcp-server.js", "scripts": {"start": "node tcp-server.js", "start:single": "node tcp-server.js --port 8080", "start:dual": "node tcp-server.js --ports 8080,8081", "start:triple": "node tcp-server.js --ports 8080,8081,8082", "start:quad": "node tcp-server.js --ports 8080,8081,8082,8083", "start:multi": "start-multi-port.bat", "test": "node test-smart-forward.js", "test-devices": "node test-devices.js", "test-mapping": "node test-id-mapping.js", "test-config": "node test-mapping-config.js", "test-two": "node test-two-devices.js", "test-smart": "node test-smart-forward.js", "config": "node config-generator.js", "config-show": "node config-generator.js --show", "server": "node tcp-server.js", "client": "node test-smart-forward.js", "build": "pkg tcp-server.js --out-path dist", "build-all": "pkg tcp-server.js --targets node18-win-x64,node18-linux-x64,node18-macos-x64 --out-path dist"}, "keywords": ["tcp", "server", "device", "forwarding", "id-based"], "author": "", "license": "MIT", "dependencies": {}, "pkg": {"assets": ["device-mapping.json"], "outputPath": "dist"}}