const net = require('net');

// 模拟设备客户端
class DeviceClient {
    constructor(deviceId, serverHost = 'localhost', serverPort = 8080) {
        this.deviceId = deviceId;
        this.deviceIdHex = deviceId.toString(16).toUpperCase().padStart(8, '0');
        this.serverHost = serverHost;
        this.serverPort = serverPort;
        this.socket = null;
        this.connected = false;
    }

    // 创建数据包
    createDataPacket(data) {
        const buffer = Buffer.alloc(4 + data.length);
        buffer.writeUInt32BE(this.deviceId, 0);
        data.copy(buffer, 4);
        return buffer;
    }

    // 连接到服务器
    connect() {
        return new Promise((resolve, reject) => {
            this.socket = new net.Socket();
            
            this.socket.connect(this.serverPort, this.serverHost, () => {
                this.connected = true;
                console.log(`[设备${this.deviceIdHex}] 已连接到服务器`);
                resolve();
            });

            this.socket.on('data', (buffer) => {
                console.log(`[设备${this.deviceIdHex}] 收到数据: ${buffer.length} 字节`);
                
                // 解析收到的数据包
                if (buffer.length >= 4) {
                    const targetDeviceId = buffer.readUInt32BE(0);
                    const data = buffer.slice(4);
                    console.log(`[设备${this.deviceIdHex}] 目标设备ID: ${targetDeviceId.toString(16).toUpperCase()}`);
                    console.log(`[设备${this.deviceIdHex}] 数据内容: ${data.toString('hex').toUpperCase()}`);
                }
            });

            this.socket.on('error', (err) => {
                console.error(`[设备${this.deviceIdHex}] 连接错误:`, err.message);
                this.connected = false;
                reject(err);
            });

            this.socket.on('close', () => {
                console.log(`[设备${this.deviceIdHex}] 连接已关闭`);
                this.connected = false;
            });
        });
    }

    // 发送数据
    sendData(data) {
        if (!this.connected || !this.socket) {
            console.error(`[设备${this.deviceIdHex}] 未连接到服务器`);
            return false;
        }

        try {
            const packet = this.createDataPacket(data);
            this.socket.write(packet);
            console.log(`[设备${this.deviceIdHex}] 发送数据: ${data.toString('hex').toUpperCase()}`);
            return true;
        } catch (error) {
            console.error(`[设备${this.deviceIdHex}] 发送数据失败:`, error);
            return false;
        }
    }

    // 断开连接
    disconnect() {
        if (this.socket) {
            this.socket.destroy();
            this.socket = null;
            this.connected = false;
        }
    }
}

// 测试函数
async function runTest() {
    console.log('启动设备测试...\n');

    // 创建多个模拟设备
    const deviceA = new DeviceClient(0x12345678); // 设备A
    const deviceB = new DeviceClient(0x87654321); // 设备B
    const deviceM = new DeviceClient(0xAABBCCDD); // 设备M
    const deviceN = new DeviceClient(0xDDCCBBAA); // 设备N

    try {
        // 连接所有设备
        console.log('连接设备到服务器...');
        await deviceA.connect();
        await new Promise(resolve => setTimeout(resolve, 500));
        
        await deviceB.connect();
        await new Promise(resolve => setTimeout(resolve, 500));
        
        await deviceM.connect();
        await new Promise(resolve => setTimeout(resolve, 500));
        
        await deviceN.connect();
        await new Promise(resolve => setTimeout(resolve, 1000));

        console.log('\n开始数据交互测试...\n');

        // 模拟数据交互
        // 设备A发送数据
        setTimeout(() => {
            const data1 = Buffer.from([0x11, 0x22, 0x33, 0x44]);
            deviceA.sendData(data1);
        }, 2000);

        // 设备B发送数据
        setTimeout(() => {
            const data2 = Buffer.from([0x55, 0x66, 0x77, 0x88]);
            deviceB.sendData(data2);
        }, 3000);

        // 设备M发送数据
        setTimeout(() => {
            const data3 = Buffer.from([0xAA, 0xBB, 0xCC, 0xDD, 0xEE]);
            deviceM.sendData(data3);
        }, 4000);

        // 设备N发送数据
        setTimeout(() => {
            const data4 = Buffer.from([0xFF, 0xEE, 0xDD, 0xCC, 0xBB, 0xAA]);
            deviceN.sendData(data4);
        }, 5000);

        // 10秒后断开所有连接
        setTimeout(() => {
            console.log('\n测试结束，断开所有连接...');
            deviceA.disconnect();
            deviceB.disconnect();
            deviceM.disconnect();
            deviceN.disconnect();
            process.exit(0);
        }, 10000);

    } catch (error) {
        console.error('测试失败:', error);
        process.exit(1);
    }
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
    runTest();
}

module.exports = DeviceClient;
