# 多端口启动说明

TCP设备转发服务器现在支持同时监听多个端口，方便在不同端口上接收设备连接。

## 🚀 启动方式

### 1. 命令行方式

#### 单端口启动
```bash
# 默认端口 8080
node tcp-server.js

# 指定单个端口
node tcp-server.js --port 9000
```

#### 多端口启动
```bash
# 双端口
node tcp-server.js --ports 8080,8081

# 三端口
node tcp-server.js --ports 8080,8081,8082

# 四端口
node tcp-server.js --ports 8080,8081,8082,8083

# 自定义端口组合
node tcp-server.js --ports 7000,8000,9000
```

#### 使用 -p 参数（简写）
```bash
# 单端口
node tcp-server.js -p 8080

# 多端口
node tcp-server.js -p 8080,8081,8082
```

### 2. 环境变量方式

```bash
# Windows
set PORT=8080,8081,8082 && node tcp-server.js

# Linux/macOS
PORT=8080,8081,8082 node tcp-server.js
```

### 3. npm scripts 方式

```bash
# 单端口
npm run start:single

# 双端口
npm run start:dual

# 三端口
npm run start:triple

# 四端口
npm run start:quad

# 交互式多端口启动
npm run start:multi
```

### 4. 交互式启动脚本

```bash
# Windows
start-multi-port.bat

# 或通过 npm
npm run start:multi
```

交互界面：
```
===== TCP设备转发服务器 - 多端口启动脚本 =====

选择启动模式:
  1. 默认端口 (8080)
  2. 双端口 (8080, 8081)
  3. 三端口 (8080, 8081, 8082)
  4. 四端口 (8080, 8081, 8082, 8083)
  5. 自定义端口
  6. 查看帮助

请选择 (1-6):
```

## 📊 启动效果

### 多端口启动日志示例
```
正在启动TCP服务器...
计划监听端口: 8080, 8081, 8082

✅ 端口 8080 启动成功
✅ 端口 8081 启动成功
✅ 端口 8082 启动成功

=== TCP服务器启动完成 ===
监听端口: 8080, 8081, 8082
等待设备连接...
---
```

### 设备连接日志
```
[1] 新连接 (端口8080): *************:54321
[1] 等待设备发送数据以确定设备ID...

[2] 新连接 (端口8081): *************:54322
[2] 等待设备发送数据以确定设备ID...
```

## 🔧 配置选项

### 命令行参数
| 参数 | 简写 | 说明 | 示例 |
|------|------|------|------|
| `--port` | `-p` | 指定端口(支持多个) | `-p 8080,8081` |
| `--ports` | 无 | 指定多个端口 | `--ports 8080,8081,8082` |
| `--config` | `-c` | 指定配置文件 | `-c my-config.json` |
| `--help` | `-h` | 显示帮助信息 | `--help` |

### 环境变量
| 变量 | 说明 | 示例 |
|------|------|------|
| `PORT` | 端口列表(逗号分隔) | `PORT=8080,8081,8082` |

## 🎯 使用场景

### 1. 设备分组
不同类型的设备连接到不同端口：
```bash
# 端口8080: 传感器设备
# 端口8081: 控制器设备  
# 端口8082: 监控设备
node tcp-server.js --ports 8080,8081,8082
```

### 2. 负载分散
将设备连接分散到多个端口：
```bash
# 4个端口分散连接压力
node tcp-server.js --ports 8080,8081,8082,8083
```

### 3. 网络隔离
不同网段的设备使用不同端口：
```bash
# 内网端口8080，外网端口9000
node tcp-server.js --ports 8080,9000
```

### 4. 测试环境
开发和测试使用不同端口：
```bash
# 开发端口8080，测试端口8081
node tcp-server.js --ports 8080,8081
```

## ⚠️ 注意事项

### 1. 端口冲突
- 确保指定的端口没有被其他程序占用
- 服务器会显示端口占用错误：`❌ 端口 8080 已被占用`

### 2. 防火墙设置
- 确保所有指定端口都允许通过防火墙
- Windows防火墙可能需要为每个端口单独配置

### 3. 资源消耗
- 每个端口都会创建独立的服务器实例
- 监听过多端口可能增加系统资源消耗

### 4. 设备配置
- 设备需要知道连接到哪个端口
- 建议为不同类型设备分配固定端口

## 🔍 故障排除

### 端口被占用
```bash
# 查看端口占用情况
netstat -an | findstr :8080

# 杀死占用端口的进程
taskkill /f /pid <进程ID>
```

### 连接测试
```bash
# 测试端口连通性
telnet localhost 8080
telnet localhost 8081
```

### 查看服务器状态
服务器启动后会显示所有监听端口的状态，确认所有端口都成功启动。

## 📦 二进制打包

多端口功能完全支持二进制打包：

```bash
# 打包
npm run build

# 运行二进制文件
dist/tcp-server.exe --ports 8080,8081,8082
```

## 🚀 最佳实践

### 1. 端口规划
- 为不同用途分配不同端口范围
- 文档化端口分配方案
- 避免使用系统保留端口

### 2. 监控
- 监控所有端口的连接状态
- 记录每个端口的设备连接数
- 设置端口健康检查

### 3. 配置管理
- 使用配置文件管理端口列表
- 为不同环境准备不同配置
- 版本控制配置文件

现在你可以灵活地在多个端口上启动TCP转发服务器了！
