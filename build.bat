@echo off
echo ===== TCP转发工具打包脚本 =====
echo.

echo 检查 pkg 是否已安装...
pkg --version >nul 2>&1
if %errorlevel% neq 0 (
    echo pkg 未安装，正在安装...
    npm install -g pkg
    if %errorlevel% neq 0 (
        echo 安装 pkg 失败，请手动安装: npm install -g pkg
        pause
        exit /b 1
    )
)

echo pkg 已安装，开始打包...
echo.

echo 创建输出目录...
if not exist dist mkdir dist

echo 打包 Windows x64 版本...
pkg tcp-server.js --targets node18-win-x64 --out-path dist
if %errorlevel% neq 0 (
    echo Windows 版本打包失败
    pause
    exit /b 1
)

echo.
echo 复制配置文件...
copy device-mapping.json dist\
copy *.md dist\

echo.
echo ===== 打包完成 =====
echo 输出目录: dist\
echo 可执行文件: dist\tcp-server.exe
echo 配置文件: dist\device-mapping.json
echo.
echo 使用方法:
echo 1. 将 dist 目录复制到目标机器
echo 2. 运行 tcp-server.exe
echo 3. 根据需要修改 device-mapping.json 配置
echo.
pause
